#!/usr/bin/env python
# -*- coding: utf-8 -*-

print("=== 简单的兑奖码配置检查 ===")

# 检查数据库中的兑奖码
import os
import sys

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.join(current_dir, 'server', 'trunk', 'game_lib'))
sys.path.insert(0, os.path.join(current_dir, 'server', 'trunk', 'llol', 'src'))

print("当前目录: %s" % current_dir)

try:
    # 设置Django环境
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')

    # 导入Django
    import django
    django.setup()

    from game_lib.models.reward import Rewards
    import datetime

    print("Django环境设置成功")

    # 获取最新的兑奖码
    now = datetime.datetime.now()
    print("当前时间: %s" % now)

    # 查询兑奖码
    cond = {'reward_obj': 'code_reward'}
    db_rewards = Rewards.query(cond)

    print("找到 %d 个兑奖码配置" % len(db_rewards))

    for item in db_rewards:
        reward_data = item.dumps(shallow=True)
        print("\n--- 兑奖码 ID: %s ---" % reward_data['id'])
        print("状态: %s" % reward_data['status'])
        print("开始时间: %s" % reward_data['start_time'])
        print("结束时间: %s" % reward_data['end_time'])
        print("是否过期: %s" % (reward_data['end_time'] < now))
        print("是否启用: %s" % (reward_data['status'] == 1))

        obj_content = reward_data.get('obj_content', {})
        enable_filters = obj_content.get('enable_filters', False)
        print("过滤条件启用: %s" % enable_filters)
        if enable_filters:
            print("  最低府邸等级: %s" % obj_content.get('min_building_level', 1))
            print("  最少注册天数: %s" % obj_content.get('min_register_days', 0))

except Exception as e:
    print("错误: %s" % str(e))
    import traceback
    traceback.print_exc()
