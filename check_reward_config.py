#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append('server/trunk/game_lib')
sys.path.append('server/trunk/llol/src')

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')

try:
    from game_lib.models.reward import Rewards
    import json
    import datetime
    
    print("=== 检查兑奖码配置 ===")
    
    # 获取所有兑奖码
    now = datetime.datetime.now()
    cond = {'status': 1, 'reward_obj': 'code_reward'}
    db_rewards = Rewards.query(cond)
    
    print("找到 %d 个兑奖码配置" % len(db_rewards))
    
    for item in db_rewards:
        reward_data = item.dumps(shallow=True)
        print("\n--- 兑奖码 ID: %s ---" % reward_data['id'])
        print("名称: %s" % reward_data.get('name_info', {}))
        print("状态: %s" % reward_data['status'])
        print("结束时间: %s" % reward_data['end_time'])
        
        obj_content = reward_data.get('obj_content', {})
        print("obj_content:")
        for key, value in obj_content.items():
            print("  %s: %s" % (key, value))
        
        # 特别检查过滤条件
        enable_filters = obj_content.get('enable_filters', False)
        print("过滤条件启用: %s" % enable_filters)
        if enable_filters:
            print("  最低府邸等级: %s" % obj_content.get('min_building_level', 1))
            print("  最少注册天数: %s" % obj_content.get('min_register_days', 0))
        
        print("是否过期: %s" % (reward_data['end_time'] < now))

except Exception as e:
    print("错误: %s" % str(e))
    import traceback
    traceback.print_exc()
