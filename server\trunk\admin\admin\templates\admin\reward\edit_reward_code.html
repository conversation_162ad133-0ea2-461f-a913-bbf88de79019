{% extends 'admin/base.html' %}
{% block content %}
<script src="{{settings.MEDIA_URL}}/js/jquery-1.12.3.min.js" type="text/javascript"></script>
<script type='text/javascript' src='{{settings.BASE_URL}}/static/js/jquery-ui-1.12.1/jquery-ui.min.js'></script>
<link rel="stylesheet" href="{{settings.BASE_URL}}/static/js/jquery-ui-1.12.1/jquery-ui.min.css" type="text/css"/>
<script type='text/javascript' src='{{settings.MEDIA_URL}}/js/reward_content.js?v=22'></script>
<script type='text/javascript'>

    function check_one_pf(pid){
        var ack = $("#"+pid).is(":checked");
        if(ack) $('#pf_'+pid).css('color', 'red');
        else   $('#pf_'+pid).css('color', 'black');
    }

    function check_all_pf(){
        var ack = $("#pf_all").is(":checked");
        var inputs = document.getElementsByName('pfs');
        for (var i = 0; i < inputs.length; i++) { 
                var cid = (inputs[i].id);
                inputs[i].checked = ack;
                if(ack) $('#pf_'+cid).css('color', 'red');
                else   $('#pf_'+cid).css('color', 'black');
            }
        } 




$(function() {
		var dates = $('#end_date').datepicker({
            dateFormat: "yy-mm-dd",
			defaultDate: "-1",
			changeMonth: true,
			numberOfMonths: 1,
            maxDate: "",
            //minDate: "2010-04-15",
            minDate: "-0",
			onSelect: function(selectedDate) {
                //statistic();
			}
		});

	});

    function chk(){ 
        if($('#id').val()==''){ 
            if($('#code_num').val()!='' ){ 
                if(parseInt($('#code_num').val()) != $('#code_num').val()){
                    alert('请正确填写兑奖码数量');
                $('#code_num').focus();
                return false;
                }
                if(parseInt($('#code_num').val()) >200000){
                alert('兑奖码数量不能超过20w');
                $('#code_num').focus();
                return false;
                }
            }else{
                alert('兑奖码数量不能为空');
                $('#code_num').focus();
                return false;
            }
        }
        if ($('#name_{{nafo_list.0.0}}').val() == ''){
            alert('{{nafo_list.0.0}}标题不能为空');
            $('#name_{{nafo_list.0.0}}').focus();
            return false;
        }
        if ($('#info_{{nafo_list.0.0}}').val() == ''){
            alert('{{nafo_list.0.0}}正文描述不能为空');
            $('#info_{{nafo_list.0.0}}').focus();
            return false;
        }
        {%for item in input_list%}
        if($('#{{item.0}}').val()!=''){ 
            flag = false;
            if(parseInt($('#{{item.0}}').val()) != $('#{{item.0}}').val()){
                alert('请正确填写{{item.1}}数量');
                $('#{{item.0}}').focus();
            return false;
            }
            if(parseInt($('#{{item.0}}').val()) >parseInt({{item.2}})){
                alert('{{item.1}}数量不能超过{{item.2}}');
                $('#{{item.0}}').focus();
            return false;
            }
        }
        {%endfor%}
        var inputs = document.getElementsByName('pfs');
        var chk_pf = false;
        for (var i = 0; i < inputs.length; i++) { 
            if (inputs[i].checked){
                chk_pf = true;
            }
            }
        if (!chk_pf){
            alert('请勾选pf ');
            return false;
        }


        if ($('#end_date').val() == ''){
            alert('请选择有效期');
            $('#end_date').focus();
            return false;
        }

        return true;

    }

    function toggleFilters() {
        var enableFilters = document.getElementById('enable_filters').checked;
        var filterOptions = document.getElementById('filter_options');
        if (enableFilters) {
            filterOptions.style.display = 'block';
        } else {
            filterOptions.style.display = 'none';
        }
    }

    // 页面加载时初始化过滤选项显示状态
    window.onload = function() {
        toggleFilters();
    };






</script>
<h2>{%if reward %}
    编辑 【{{nafo_list.0.1}}】
    {%else%}
    添加
    {%endif%}
    兑奖码发奖
</h2>
<br/>
<form action="{{settings.BASE_URL}}/admin/reward/edit_reward_code_p/" method="post" enctype="multipart/form-data" onsubmit="return chk();">
<table width="100%">
    <tr>
        <td width="10%">
            标题正文
        </td>
        <td>
         {%for item in nafo_list%}
         <tr>
             <td>
                 name-{{item.0}}
             </td>
             <td>
                 <input type="txt" name="name_{{item.0}}" id="name_{{item.0}}" value="{{item.1}}" /><br/>
             </td>
         </tr>
         <tr>
             <td>
                 info-{{item.0}}
             </td>
             <td>
                 <textarea id="info_{{item.0}}" name="info_{{item.0}}" style="width: 90%; height: 30px">{{item.2}}</textarea>
             </td>
         </tr>
         {%endfor%}

        </td>
    </tr> 


    <tr>
        <td>
            发奖对象
        </td>
        <td>
<p>
            <input type="checkbox" name="pf_all" id="pf_all" value="" onclick="check_all_pf()"/>
            <font color="red">pf全选</font>
            <br />
            {%for item in pf_list%}
            <input type="checkbox" name="pfs" id="{{item.0}}" value="{{item.0}}" {%if item.1 %}checked=checked{%endif%} onclick="check_one_pf('{{item.0}}')"/>
                        <font{%if item.1%} color="red"{%endif%} id='pf_{{item.0}}'>{{item.0}}</font>
            {%endfor%}
        </td>
        </tr>



    <tr>
        <td>
            分组
        </td>
        <td>
            <input type="txt" name="group" id="group" value="{{reward.obj_content.group|default:-1}}" /> 大于0的数字表示分组, -1表示不分组

        </td>
    </tr> 
    <tr>
        <td>
            兑奖码
        </td>
        <td>
            <input name="code_num" id="code_num" size=5 value="{{reward.obj_content.code_num}}"/> (兑奖码数量)
            <input type='checkbox' {%ifequal reward.obj_content.repeat_use '1'%}checked=checked{%endifequal%} name='repeat_use' value='1' id='repeat_use'/>可重复使用(兑奖码数量次数)

        <td/>
    </tr>

    <!-- 兑奖码使用条件 -->
    <tr>
        <td>
            使用条件
        </td>
        <td>
            <input type="checkbox" id="enable_filters" name="enable_filters" value="1"
                {%if reward.obj_content.enable_filters%}checked{%endif%} onclick="toggleFilters()"/> 启用使用条件限制
            <br/>
            <div id="filter_options" style="display:none; margin-top: 10px;">
                <table>
                    <tr>
                        <td>府邸等级：</td>
                        <td>
                            最低 <input type="number" name="min_building_level" value="{{reward.obj_content.min_building_level|default:1}}" min="1" max="999" style="width: 60px;"/> 级
                            <span style="color: #666;">（建筑001等级）</span>
                        </td>
                    </tr>
                    <tr>
                        <td>注册天数：</td>
                        <td>
                            最少 <input type="number" name="min_register_days" value="{{reward.obj_content.min_register_days|default:0}}" min="0" max="9999" style="width: 60px;"/> 天
                            <span style="color: #666;">（注册多少天后可用）</span>
                        </td>
                    </tr>
                </table>
                <div style="color: #666; margin-top: 5px; font-size: 12px;">
                    说明：启用后，只有满足条件的玩家才能使用兑奖码
                </div>
            </div>
        </td>
    </tr>

    {%include 'admin/reward/reward_content.html'%}



    <tr>
        <td>
            有效期
        </td>
        <td>
            <input readonly size="7" name="end_date"  id="end_date" value="{{reward.end_date}}" /> 
    <select name="end_hour">
        {% for item in hours %}
        <option {%ifequal reward.end_hour item%}selected=selected{%endifequal%} value="{{item}}">{{item}}</option>
        {% endfor %}
    </select>
    :
    <select name="end_minute">
        {% for item in minutes %}
        <option {%ifequal reward.end_minute item%}selected=selected{%endifequal%} value="{{item}}">{{item}}</option>
        {% endfor %}
    </select>

        </td>
    </tr>
    <tr>
        <td>
        </td>
        <td>
            <input type="hidden" name="id" id="id" value="{{reward.id}}" type="txt"/>
            <input type="submit" value="提交"/>
        </td>
    </tr>
</table>
</form>


{% endblock %}
