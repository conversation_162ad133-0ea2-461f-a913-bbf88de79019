#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单的兑奖码配置检查脚本
"""

import os
import sys

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.join(current_dir, 'server', 'trunk', 'game_lib'))
sys.path.insert(0, os.path.join(current_dir, 'server', 'trunk', 'llol', 'src'))

print("=== 兑奖码配置调试 ===")
print("当前目录:", current_dir)

# 检查关键文件是否存在
key_files = [
    'server/trunk/admin/admin/views/app.log',
    'server/service/app.log',
    'server/trunk/admin/admin/views/reward.py'
]

for file_path in key_files:
    full_path = os.path.join(current_dir, file_path)
    if os.path.exists(full_path):
        size = os.path.getsize(full_path)
        print("✓ %s 存在 (大小: %d 字节)" % (file_path, size))
        
        # 如果是日志文件，显示最后几行
        if file_path.endswith('.log'):
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    if lines:
                        print("  最后几行内容:")
                        for line in lines[-3:]:
                            print("    " + line.strip())
                    else:
                        print("  文件为空")
            except:
                try:
                    with open(full_path, 'r') as f:
                        lines = f.readlines()
                        if lines:
                            print("  最后几行内容:")
                            for line in lines[-3:]:
                                print("    " + line.strip())
                        else:
                            print("  文件为空")
                except Exception as e:
                    print("  读取失败:", str(e))
    else:
        print("✗ %s 不存在" % file_path)

# 尝试写入测试日志
test_log_path = os.path.join(current_dir, 'test_debug.log')
try:
    with open(test_log_path, 'w', encoding='utf-8') as f:
        f.write("测试日志写入成功\n")
    print("✓ 日志写入测试成功:", test_log_path)
except Exception as e:
    print("✗ 日志写入测试失败:", str(e))

print("\n=== 建议的调试步骤 ===")
print("1. 检查管理后台是否正常运行")
print("2. 在管理后台编辑兑奖码时查看网络请求")
print("3. 检查数据库中的兑奖码配置")
print("4. 在游戏中实际使用兑奖码进行测试")
